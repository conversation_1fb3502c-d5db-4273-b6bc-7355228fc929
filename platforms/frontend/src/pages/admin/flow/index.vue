<template>
    <div>
        <el-table :data="flows" style="width: 100%">
            <el-table-column prop="id" label="id" />
            <el-table-column prop="name" label="流程名称" />
            <el-table-column prop="displayName" label="中文名称" />
            <el-table-column prop="templateId" label="templateId" />
            <el-table-column label="插件&分支关系">
                <template #default="{ row }">
                    <router-link :to="`/admin/flow/${row.id}`">查看详情</router-link>
                </template>
            </el-table-column>
            <!-- 其他列 -->
        </el-table>
        <el-pagination
            @current-change="handleCurrentChange"
            :current-page="currentPage"
            :page-size="pageSize"
            :total="totalFlows"
            layout="total, prev, pager, next, jumper"
        />
    </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { getAllFlows } from '@/services/flow';
import type { Flow } from 'lingx-server/libs/core/src/models/Flow';

const flows = ref<Flow[]>([]);
const currentPage = ref(1);
const pageSize = ref(10);
const totalFlows = ref(0);

const fetchFlows = async () => {
    const response = await getAllFlows({
        page: currentPage.value,
        size: pageSize.value
    });
    flows.value = response.list; // 假设返回的数据在 response.data 中
    totalFlows.value = response.count; // 假设总数在 response.total 中
};

const handleCurrentChange = (page: number) => {
    currentPage.value = page;
    fetchFlows(); // 根据当前页重新获取数据
};

onMounted(() => {
    fetchFlows();
});
</script>

<route lang="yaml">
meta:
    layout: admin
</route>
